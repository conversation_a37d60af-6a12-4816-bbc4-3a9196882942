/**
 * @fileoverview Concrete implementation of encryption service for login credentials
 * <AUTHOR>
 * @version 1.0.0
 */

import { Injectable } from '@angular/core';
import { Observable, from, switchMap, map, forkJoin } from 'rxjs';
import * as CryptoJS from 'crypto-js';
import { JSEncrypt } from 'jsencrypt';
import { ApiServiceBase } from 'lib-app-core';

// Base interfaces for encryption services
export interface BaseEncryptionConfig {
    siteId: number;
    applicationName: string;
    applicationVersion: string;
    applicationIdentifier: string;
}

export interface BaseEncryptionResult {
    [key: string]: string | number;
}

// Login-specific interfaces extending base interfaces
export interface LoginEncryptionConfig extends BaseEncryptionConfig {
    loginId: string;
    password: string;
    machineName: string;
}

export interface EncryptedLoginRequest extends BaseEncryptionResult {
    EncryptedUserName: string;
    EncryptedPassword: string;
    Application: string;
    Version: string;
    Identifier: string;
    SiteId: number;
    EncryptedMachineName: string;
    EncryptedKeyMaterial: string;
}

export interface ServerTimeResponse {
    data: number;
}

export interface PublicKeyResponse {
    data: string;
}

export interface PublicKeyRequestParams extends Record<string, string | number> {
    siteId: string;
    application: string;
    version: string;
    identifier: string;
    format: string;
}

/**
 * Concrete implementation of encryption service specifically for login credentials
 * Implements multi-layer encryption using AES and RSA
 * Extends ApiServiceBase to reuse existing HTTP infrastructure
 */
@Injectable({
    providedIn: 'root',
})
export class LoginEncryptionService extends ApiServiceBase {
    private aesKey: CryptoJS.lib.WordArray | null = null;
    private jsEncrypt: JSEncrypt | null = null;
    private secondsSinceEpoch: number | null = null;

    constructor() {
        // Use ApiServiceBase constructor - we don't need data transfer for encryption
        super('login_encryption_data', 'LOGIN_ENCRYPTION_ENDPOINTS');
    }

    /**
     * Initialize encryption by getting public key and timestamp
     */
    load(): Observable<void> {
        return forkJoin({
            time: this.getSecondsSinceEpoch(),
            key: this.loadJSEncrypt().pipe(
                switchMap(() => this.getPublicKey())
            ),
        }).pipe(map(() => void 0));
    }

    /**
     * Encrypts login credentials using the multi-layer encryption system
     */
    encrypt(config: LoginEncryptionConfig): Observable<EncryptedLoginRequest> {
        this.validateEncryptionConfig(config, ['siteId', 'applicationName', 'applicationVersion', 'applicationIdentifier', 'loginId', 'password', 'machineName']);

        return this.load().pipe(
            switchMap(() => this.buildEncryptedLoginRequest(config))
        );
    }

    /**
     * Get timestamp from server
     */
    private getSecondsSinceEpoch(): Observable<void> {
        const url = `${this._envService.parafaitApiBaseUrl}/Login/SecondsSinceEpoch`;

        return this._http.get<ServerTimeResponse>(url).pipe(
            map((response) => {
                this.secondsSinceEpoch = response.data;
                this.logEncryptionOperation('Retrieved server timestamp', this.secondsSinceEpoch?.toString());
            })
        );
    }

    /**
     * Load JSEncrypt library dynamically
     */
    private loadJSEncrypt(): Observable<void> {
        return from(
            fetch('https://raw.githubusercontent.com/travist/jsencrypt/master/bin/jsencrypt.js')
                .then((response) => response.text())
                .then((text) => {
                    // Execute the JSEncrypt library
                    new Function(text)();
                    this.jsEncrypt = new (window as unknown as { JSEncrypt: new () => JSEncrypt }).JSEncrypt();
                    this.logEncryptionOperation('JSEncrypt library loaded successfully');
                })
                .catch((error) => this.handleEncryptionError(error, 'JSEncrypt loading'))
        );
    }

    /**
     * Get public key from server
     */
    private getPublicKey(): Observable<void> {
        const url = `${this._envService.parafaitApiBaseUrl}/Login/PublicKey`;
        const params = {
            siteId: '1010',
            application: 'WaiverWebsite',
            version: '*********',
            identifier: 'WaiverWebsite',
            format: 'PEM',
        };

        return this._http.get<PublicKeyResponse>(url, { params }).pipe(
            map((response) => {
                const pemPublicKey = response.data;
                if (this.jsEncrypt) {
                    this.jsEncrypt.setPublicKey(pemPublicKey);
                    this.logEncryptionOperation('Public key loaded successfully');
                } else {
                    throw new Error('JSEncrypt not initialized');
                }
            })
        );
    }

    /**
     * Build the encrypted login request
     */
    private buildEncryptedLoginRequest(config: LoginEncryptionConfig): Observable<EncryptedLoginRequest> {
        try {
            // Generate AES key if not already generated
            if (!this.aesKey) {
                this.aesKey = CryptoJS.lib.WordArray.random(256/8);
            }

            const base64AesKey = this.aesKey.toString(CryptoJS.enc.Base64);
            const keyMaterial = `${base64AesKey}|${this.secondsSinceEpoch}`;
            this.logEncryptionOperation('Generated key material');

            const encryptedKeyMaterial = this.rsaEncrypt(keyMaterial);
            const encryptedLoginId = this.aesEncrypt(config.loginId);
            const encryptedPassword = this.aesEncrypt(config.password);
            const encryptedMachineName = this.aesEncrypt(config.machineName);

            const loginRequest: EncryptedLoginRequest = {
                EncryptedUserName: encryptedLoginId,
                EncryptedPassword: encryptedPassword,
                Application: config.applicationName,
                Version: config.applicationVersion,
                Identifier: config.applicationIdentifier,
                SiteId: config.siteId,
                EncryptedMachineName: encryptedMachineName,
                EncryptedKeyMaterial: encryptedKeyMaterial,
            };

            this.logEncryptionOperation('Built encrypted login request');
            return new Observable((observer) => {
                observer.next(loginRequest);
                observer.complete();
            });
        } catch (error) {
            return new Observable((observer) => {
                observer.error(this.handleEncryptionError(error, 'building encrypted login request'));
            });
        }
    }

    /**
     * AES encryption for sensitive data
     */
    private aesEncrypt(message: string): string {
        if (!this.aesKey) {
            throw new Error('AES key not initialized');
        }

        try {
            const iv = CryptoJS.enc.Hex.parse('00000000000000000000000000000000');
            const encrypted = CryptoJS.AES.encrypt(message, this.aesKey, {
                iv: iv,
                mode: CryptoJS.mode.CBC,
                padding: CryptoJS.pad.Pkcs7,
            });

            const result = encrypted.ciphertext.toString(CryptoJS.enc.Base64);
            this.logEncryptionOperation('AES encrypted data', `${message.substring(0, 3)}...`);
            return result;
        } catch (error) {
            throw this.handleEncryptionError(error, 'AES encryption');
        }
    }

    /**
     * RSA encryption for key material
     */
    private rsaEncrypt(message: string): string {
        if (!this.jsEncrypt) {
            throw new Error('JSEncrypt not initialized');
        }

        try {
            const result = this.jsEncrypt.encrypt(message);
            if (!result) {
                throw new Error('RSA encryption failed - no result returned');
            }
            this.logEncryptionOperation('RSA encrypted key material');
            return result;
        } catch (error) {
            throw this.handleEncryptionError(error, 'RSA encryption');
        }
    }

    /**
     * Reset encryption state (useful for testing or re-initialization)
     */
    resetEncryptionState(): void {
        this.aesKey = null;
        this.jsEncrypt = null;
        this.secondsSinceEpoch = null;
        this.logEncryptionOperation('Encryption state reset');
    }

    /**
     * Check if encryption is properly initialized
     */
    isInitialized(): boolean {
        return this.aesKey !== null && this.jsEncrypt !== null && this.secondsSinceEpoch !== null;
    }

    /**
     * Validate that required configuration fields are present
     */
    private validateEncryptionConfig(config: LoginEncryptionConfig, requiredFields: (keyof LoginEncryptionConfig)[]): void {
        const missingFields = requiredFields.filter(field =>
            config[field] === undefined || config[field] === null || config[field] === ''
        );

        if (missingFields.length > 0) {
            throw new Error(`Missing required configuration fields: ${missingFields.join(', ')}`);
        }
    }

    /**
     * Log encryption operations
     */
    private logEncryptionOperation(operation: string, details?: string): void {
        console.log(`[LoginEncryption] ${operation}${details ? ': ' + details : ''}`);
    }

    /**
     * Handle encryption errors consistently
     */
    private handleEncryptionError(error: unknown, operation: string): never {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        const fullMessage = `LoginEncryption ${operation} failed: ${errorMessage}`;
        console.error(fullMessage, error);
        throw new Error(fullMessage);
    }
}
