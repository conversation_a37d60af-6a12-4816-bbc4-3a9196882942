import { Injectable, inject } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, from, switchMap, map, forkJoin } from 'rxjs';
import { EnvService } from 'lib-app-core';
import * as CryptoJS from 'crypto-js';
import { JSEncrypt } from 'jsencrypt';

export interface EncryptionConfig {
    siteId: number;
    applicationName: string;
    applicationVersion: string;
    applicationIdentifier: string;
    loginId: string;
    password: string;
    machineName: string;
}

export interface EncryptedLoginRequest {
    EncryptedLoginId: string; // Encrypted login ID
    EncryptedPassword: string; // Encrypted password
    Application: string;
    Version: string;
    Identifier: string;
    SiteId: number;
    EncryptedMachineName: string; // Encrypted machine name
    EncryptedKeyMaterial: string; // Encrypted key material
}

@Injectable({
    providedIn: 'root',
})
export class EncryptionService {
    private readonly http = inject(HttpClient);
    private readonly envService = inject(EnvService);

    private aesKey: CryptoJS.lib.WordArray | null = null;
    private jsEncrypt: JSEncrypt | null = null;
    private secondsSinceEpoch: number | null = null;

    /**
     * Encrypts login credentials using the multi-layer encryption system
     */
    encryptLoginCredentials(
        config: EncryptionConfig
    ): Observable<EncryptedLoginRequest> {
        return this.initializeEncryption().pipe(
            switchMap(() => this.buildEncryptedLoginRequest(config))
        );
    }

    /**
     * Initialize encryption by getting public key and timestamp
     */
    //  private initializeEncryption(): Observable<void> {
    //   return this.getSecondsSinceEpoch().pipe(
    //     switchMap(() => this.loadJSEncrypt()),
    //     switchMap(() => this.getPublicKey()),
    //     map(() => void 0)
    //   );
    // }
    private initializeEncryption(): Observable<void> {
        return forkJoin({
            time: this.getSecondsSinceEpoch(),
            key: this.loadJSEncrypt().pipe(
                switchMap(() => this.getPublicKey())
            ),
        }).pipe(map(() => void 0));
    }

    /**
     * Get timestamp from server
     */
    private getSecondsSinceEpoch(): Observable<void> {
        const url = `${this.envService.parafaitApiBaseUrl}/Login/SecondsSinceEpoch`;

        return this.http.get<{ data: number }>(url).pipe(
            map((response) => {
                this.secondsSinceEpoch = response.data;
                console.log('Seconds since epoch:', this.secondsSinceEpoch);
            })
        );
    }

    /**
     * Load JSEncrypt library dynamically
     */
    private loadJSEncrypt(): Observable<void> {
        return from(
            fetch(
                'https://raw.githubusercontent.com/travist/jsencrypt/master/bin/jsencrypt.js'
            )
                .then((response) => response.text())
                .then((text) => {
                    // Execute the JSEncrypt library
                    new Function(text)();
                    this.jsEncrypt = new (window as any).JSEncrypt();
                    console.log('JSEncrypt loaded successfully');
                })
        );
    }

    /**
     * Get public key from server
     */
    private getPublicKey(): Observable<void> {
        const url = `${this.envService.parafaitApiBaseUrl}/Login/PublicKey`;
        const params = {
            siteId: '1010',
            application: 'WaiverWebsite',
            version: '*********',
            identifier: 'WaiverWebsite',
            format: 'PEM',
        };

        return this.http.get<{ data: string }>(url, { params }).pipe(
            map((response) => {
                const pemPublicKey = response.data;
                if (this.jsEncrypt) {
                    this.jsEncrypt.setPublicKey(pemPublicKey);
                    console.log('Public key loaded successfully', pemPublicKey);
                }
            })
        );
    }

    /**
     * Build the encrypted login request
     */
    private buildEncryptedLoginRequest(
        config: EncryptionConfig
    ): Observable<EncryptedLoginRequest> {
        // Generate AES key if not already generated
        if (!this.aesKey) {
            this.aesKey = CryptoJS.lib.WordArray.random(256/8);
        }

        const base64AesKey = this.aesKey.toString(CryptoJS.enc.Base64);
        const keyMaterial = `${base64AesKey}|${this.secondsSinceEpoch}`;
        console.log('KeyMaterial:', keyMaterial);

        const encryptedKeyMaterial = this.rsaEncrypt(keyMaterial);
        const encryptedLoginId = this.aesEncrypt(config.loginId);
        const encryptedPassword = this.aesEncrypt(config.password);
        const encryptedMachineName = this.aesEncrypt(config.machineName);

        const loginRequest: EncryptedLoginRequest = {
            EncryptedLoginId: encryptedLoginId,
            EncryptedPassword: encryptedPassword,
            Application: config.applicationName,
            Version: config.applicationVersion,
            Identifier: config.applicationIdentifier,
            SiteId: config.siteId,
            EncryptedMachineName: encryptedMachineName,
            EncryptedKeyMaterial: encryptedKeyMaterial,
        };

        console.log('Encrypted login request:', loginRequest);
        return new Observable((observer) => {
            observer.next(loginRequest);
            observer.complete();
        });
    }

    /**
     * AES encryption for sensitive data
     */
    private aesEncrypt(message: string): string {
        if (!this.aesKey) {
            throw new Error('AES key not initialized');
        }

        const iv = CryptoJS.enc.Hex.parse('00000000000000000000000000000000');
        const encrypted = CryptoJS.AES.encrypt(message, this.aesKey, {
            iv: iv,
            mode: CryptoJS.mode.CBC,
            padding: CryptoJS.pad.Pkcs7,
        });

        const result = encrypted.ciphertext.toString(CryptoJS.enc.Base64);
        console.log(`${message}(base64): ${result}`);
        return result;
    }

    /**
     * RSA encryption for key material
     */
    private rsaEncrypt(message: string): string {
        if (!this.jsEncrypt) {
            throw new Error('JSEncrypt not initialized');
        }

        const result = this.jsEncrypt.encrypt(message);
        console.log(`KeyMaterial(base64): ${result}`);
        return result || '';
    }
}
