import { Injectable, inject } from '@angular/core';
import { Observable } from 'rxjs';
import { LoginEncryptionService } from './login-encryption.service';

// Base interfaces for encryption services
export interface BaseEncryptionConfig {
    siteId: number;
    applicationName: string;
    applicationVersion: string;
    applicationIdentifier: string;
}

export interface BaseEncryptionResult {
    [key: string]: string | number;
}

export interface ServerTimeResponse {
    data: number;
}

export interface PublicKeyResponse {
    data: string;
}

export interface PublicKeyRequestParams extends Record<string, string | number> {
    siteId: string;
    application: string;
    version: string;
    identifier: string;
    format: string;
}

// Login-specific interfaces extending base interfaces
export interface LoginEncryptionConfig extends BaseEncryptionConfig {
    loginId: string;
    password: string;
    machineName: string;
}

export interface EncryptedLoginRequest extends BaseEncryptionResult {
    EncryptedLoginId: string;
    EncryptedPassword: string;
    Application: string;
    Version: string;
    Identifier: string;
    SiteId: number;
    EncryptedMachineName: string;
    EncryptedKeyMaterial: string;
}

// Legacy interface for backward compatibility
export interface EncryptionConfig extends LoginEncryptionConfig {}

@Injectable({
    providedIn: 'root',
})
export class EncryptionService {
    private readonly loginEncryptionService = inject(LoginEncryptionService);

    /**
     * Encrypts login credentials using the multi-layer encryption system
     * @deprecated Use LoginEncryptionService directly for better type safety and modularity
     */
    encryptLoginCredentials(
        config: EncryptionConfig
    ): Observable<EncryptedLoginRequest> {
        return this.loginEncryptionService.encrypt(config);
    }

}
