/**
 * @fileoverview Abstract base class for encryption services following ApiServiceBase pattern
 * <AUTHOR>
 * @version 1.0.0
 */

import { inject, Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { EnvService } from 'lib-app-core';

// Base interfaces for encryption services
export interface BaseEncryptionConfig {
    siteId: number;
    applicationName: string;
    applicationVersion: string;
    applicationIdentifier: string;
}

export interface BaseEncryptionResult {
    [key: string]: string | number;
}

/**
 * Abstract base class for encryption services
 * Follows the same pattern as ApiServiceBase but without storage mechanisms
 * Uses generics to ensure type safety across different encryption implementations
 */
@Injectable()
export abstract class EncryptionServiceBase<
    TConfig extends BaseEncryptionConfig,
    TResult extends BaseEncryptionResult
> {
    protected readonly http = inject(HttpClient);
    protected readonly envService = inject(EnvService);

    private _encryptionConfig: TConfig | null = null;

    constructor(
        private _configKey: string,
        private _encryptionType: string
    ) {}

    /**
     * Abstract method to initialize encryption (load keys, timestamps, etc.)
     * Must be implemented by concrete encryption services
     */
    abstract load(): Observable<void>;

    /**
     * Abstract method to perform the main encryption operation
     * Must be implemented by concrete encryption services
     */
    abstract encrypt(config: TConfig): Observable<TResult>;

    /**
     * Build encryption configuration parameters
     * Similar to buildApiParams in ApiServiceBase
     */
    buildEncryptionConfig(config: TConfig): void {
        this._encryptionConfig = config;
    }

    /**
     * Get the current encryption configuration
     */
    protected get encryptionConfig(): TConfig {
        if (!this._encryptionConfig) {
            throw new Error('Encryption configuration not set. Call buildEncryptionConfig() first.');
        }
        return this._encryptionConfig;
    }

    /**
     * Check if encryption configuration is set
     */
    protected get hasEncryptionConfig(): boolean {
        return this._encryptionConfig !== null;
    }

    /**
     * Get the configuration key for this encryption service
     */
    protected get configKey(): string {
        return this._configKey;
    }

    /**
     * Get the encryption type identifier
     */
    protected get encryptionType(): string {
        return this._encryptionType;
    }

    /**
     * Helper method to get API URL using environment service
     * Similar to getApiUrl in ApiServiceBase
     */
    protected getApiUrl(endpoint: string, params?: { [key: string]: string | number }): string {
        let url = `${this.envService.parafaitApiBaseUrl}${endpoint}`;
        
        if (params) {
            const queryParams = new URLSearchParams();
            Object.entries(params).forEach(([key, value]) => {
                queryParams.append(key, String(value));
            });
            url += `?${queryParams.toString()}`;
        }
        
        return url;
    }

    /**
     * Helper method to make HTTP GET requests with proper typing
     */
    protected httpGet<T>(url: string, options?: { params?: Record<string, string | number> }): Observable<T> {
        if (options?.params) {
            const queryParams = new URLSearchParams();
            Object.entries(options.params).forEach(([key, value]) => {
                queryParams.append(key, String(value));
            });
            url += `?${queryParams.toString()}`;
        }

        return this.http.get<T>(url);
    }

    /**
     * Helper method to make HTTP POST requests with proper typing
     */
    protected httpPost<T>(url: string, body: unknown): Observable<T> {
        return this.http.post<T>(url, body);
    }

    /**
     * Validate that required configuration fields are present
     */
    protected validateConfig(config: TConfig, requiredFields: (keyof TConfig)[]): void {
        const missingFields = requiredFields.filter(field => 
            config[field] === undefined || config[field] === null || config[field] === ''
        );

        if (missingFields.length > 0) {
            throw new Error(`Missing required configuration fields: ${missingFields.join(', ')}`);
        }
    }

    /**
     * Log encryption operations (can be overridden by concrete implementations)
     */
    protected logOperation(operation: string, details?: string): void {
        console.log(`[${this.encryptionType}] ${operation}${details ? ': ' + details : ''}`);
    }

    /**
     * Handle encryption errors consistently
     */
    protected handleEncryptionError(error: unknown, operation: string): never {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        const fullMessage = `${this.encryptionType} ${operation} failed: ${errorMessage}`;
        console.error(fullMessage, error);
        throw new Error(fullMessage);
    }
}
