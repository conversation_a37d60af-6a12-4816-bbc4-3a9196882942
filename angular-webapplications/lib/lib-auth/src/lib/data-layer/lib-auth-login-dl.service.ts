/**
 * @fileoverview Data layer service for handling login API calls
 * <AUTHOR>
 * @version 1.0.0
 */
import { Injectable } from '@angular/core';
import { ApiServiceBase } from 'lib-app-core';
import { Observable, switchMap } from 'rxjs';
import { LoginEncryptionService } from '../services/login-encryption.service';
import { LoginEncryptionConfig } from '../services/web-crypto-encryption.service';
import { DEFAULT_APP_CONFIG_TOKEN } from 'lib-app-core';
import { inject } from '@angular/core';
import { LibAuthLoginAPIParams } from '../interfaces/login-form.interface';


@Injectable()
export class LibAuthLoginDL extends ApiServiceBase {
    private _apiParams!: LibAuthLoginAPIParams;
    private readonly loginEncryptionService = inject(LoginEncryptionService);
    private readonly defaultAppConfig = inject(DEFAULT_APP_CONFIG_TOKEN);

    constructor() {
        // Call the parent constructor with the specific API endpoint and action type
        super('user_login_data', 'USER_LOGIN');
        this.init();
    }

    // Builds the API parameters for the login request
    // This method is called by the business layer to set the parameters before making the API call
    buildApiParams(data: LibAuthLoginAPIParams) {
        this._apiParams = data;
    }

    // Returns the API URL for the login request
    // This method is used to construct the URL for the API call
    load(): Observable<any> {
        const url = `${this.getApiUrl()}`;

         // Create encryption config
         const encryptionConfig: LoginEncryptionConfig = {
            siteId: this.defaultAppConfig['siteId'] || 1010,
            applicationName: 'OnlineWaiver',
            applicationVersion: '1.0.0',
            applicationIdentifier: 'waiver-app',
            loginId: this._apiParams.UserName,
            password: this._apiParams.Password,
            machineName: this.defaultAppConfig['machineName'] || 'WebClient'
        };

        //return this._http.post<any>(url, loginPayload)
        return this.loginEncryptionService.encrypt(encryptionConfig).pipe(
            switchMap(encryptedPayload => {
                return this._http.post<any>(url, encryptedPayload);
            })
        );

    }
}
